import Home from "../src/Home";
import renderer from "react-test-renderer";
import React from "react";
import { render, fireEvent, act } from "@testing-library/react-native";
import TrackPlayer from "react-native-track-player";
import { Alert, Linking } from "react-native";
import { Constants } from "../src/utils/Constants";
import NetInfo from "@react-native-community/netinfo";

describe("Home component", () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders correctly", async () => {
        const tree = renderer.create(<Home />);
        expect(tree).toMatchSnapshot();
    });
    
    test("should show play button", () => {
        const { getByTestId } = render(<Home />);
        const playButton = getByTestId("play-button");
        expect(playButton).toBeTruthy();
    });

    // it("should handle play/pause correctly", async () => {
    //     const { getByTestId } = render(<Home />);
    //     expect(TrackPlayer.pause).toHaveBeenCalled();
    //     await act(async () => {
    //         fireEvent(getByTestId("play-button"), "press");
    //     });
    //     expect(TrackPlayer.play).toHaveBeenCalled();
    // });
  
    it("should handle play/pause correctly with network connection", async () => {
        // Mock network connection as connected
        (NetInfo.fetch as jest.Mock).mockResolvedValue({ isConnected: true });
    
        const { getByTestId } = render(<Home />);
        await act(async () => {
            fireEvent(getByTestId("play-button"), "press");
        });
    
        // Assert TrackPlayer.play() is called
        expect(TrackPlayer.play).toHaveBeenCalled();
    });
    
    it("should handle play/pause correctly without network connection", async () => {
        // Mock network connection as not connected
        (NetInfo.fetch as jest.Mock).mockResolvedValue({ isConnected: false });
        
        // expect(TrackPlayer.play).not.toHaveBeenCalled();
        const { getByTestId } = render(<Home />);
        try {
            await act(async () => {
                fireEvent(getByTestId("play-button"), "press");
            });
            expect(TrackPlayer.play).not.toHaveBeenCalled();
            expect(Alert.alert).toHaveBeenCalledWith(
                Constants.networkAlert.title,
                Constants.networkAlert.message,
                expect.any(Array)
            );
        } catch {
            console.log("catching error");
        }
    });

    it("should handle external URL linking", async () => {
        const { getByTestId } = render(<Home />);
  
        fireEvent.press(getByTestId("Facebook"));
        expect(Linking.openURL).toHaveBeenCalledWith(Constants.externalLinks.fbUrl);
  
        fireEvent.press(getByTestId("Instagram"));
        expect(Linking.openURL).toHaveBeenCalledWith(Constants.externalLinks.instalUrl);
  
        fireEvent.press(getByTestId("Twitter"));
        expect(Linking.openURL).toHaveBeenCalledWith(Constants.externalLinks.twitterUrl);
  
        fireEvent.press(getByTestId("YouTube"));
        expect(Linking.openURL).toHaveBeenCalledWith(Constants.externalLinks.youtubeUrl);
  
        fireEvent.press(getByTestId("Snapchat"));
        expect(Linking.openURL).toHaveBeenCalledWith(Constants.externalLinks.snapChatUrl);
    });
});