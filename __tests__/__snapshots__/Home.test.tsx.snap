// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Home component renders correctly 1`] = `
<View
  style={
    {
      "backgroundColor": "white",
      "flex": 1,
    }
  }
>
  <Image
    source={
      {
        "testUri": "../../../assets/pa.png",
      }
    }
    style={
      [
        {
          "alignSelf": "flex-end",
          "height": 200,
          "marginBottom": 26.5,
          "marginRight": 20,
          "width": 600,
        },
        {
          "marginTop": 20,
        },
      ]
    }
  />
  <View
    style={
      {
        "flexDirection": "row",
        "height": 5,
        "width": 750,
      }
    }
  >
    <View
      style={
        {
          "backgroundColor": "#DFDFDF",
          "height": 5,
          "width": 240,
        }
      }
    />
    <View
      style={
        {
          "backgroundColor": "#d2003e",
          "height": 5,
          "width": 540,
        }
      }
    />
  </View>
  <View
    style={
      {
        "alignItems": "center",
        "height": 333.5,
        "width": 750,
      }
    }
  >
    <Image
      resizeMode="contain"
      source={
        {
          "testUri": "../../../assets/Br.png",
        }
      }
      style={
        {
          "height": 200,
          "marginTop": 20,
          "resizeMode": "contain",
          "width": 337.5,
        }
      }
    />
  </View>
  <Image
    source={
      {
        "testUri": "../../../assets/tT.png",
      }
    }
    style={
      {
        "bottom": 0,
        "height": 533.5,
        "position": "absolute",
        "width": 750,
      }
    }
  />
  <View
    style={
      {
        "flex": 1,
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "alignSelf": "center",
          "backgroundColor": "#d2003e",
          "borderRadius": 225,
          "height": 225,
          "justifyContent": "center",
          "marginTop": 30,
          "width": 225,
        }
      }
    >
      <View
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={true}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "opacity": 1,
          }
        }
        testID="play-button"
      >
        <Image
          source={
            {
              "testUri": "../../../assets/Vd.png",
            }
          }
        />
      </View>
    </View>
    <View
      style={
        {
          "alignItems": "center",
          "flex": 1,
        }
      }
    >
      <View
        style={
          {
            "alignItems": "center",
            "backgroundColor": "#d2003e",
            "borderBottomLeftRadius": 10,
            "borderBottomRightRadius": 10,
            "borderTopLeftRadius": 10,
            "bottom": 20,
            "flexDirection": "row",
            "height": 106.5,
            "justifyContent": "space-around",
            "position": "absolute",
            "width": 670,
          }
        }
      >
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "height": 80,
              "opacity": 1,
              "resizeMode": "contain",
              "width": 80,
            }
          }
        >
          <Image
            source={
              {
                "testUri": "../../../assets/aa.png",
              }
            }
            style={
              {
                "height": 80,
                "resizeMode": "contain",
                "width": 80,
              }
            }
          />
        </View>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "opacity": 1,
            }
          }
        >
          <Image
            source={
              {
                "testUri": "../../../assets/g6.png",
              }
            }
            style={
              {
                "height": 80,
                "resizeMode": "contain",
                "width": 80,
              }
            }
          />
        </View>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "opacity": 1,
            }
          }
        >
          <Image
            source={
              {
                "testUri": "../../../assets/_e1.png",
              }
            }
            style={
              {
                "height": 80,
                "resizeMode": "contain",
                "width": 80,
              }
            }
          />
        </View>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "opacity": 1,
            }
          }
        >
          <Image
            source={
              {
                "testUri": "../../../assets/Gz.png",
              }
            }
            style={
              {
                "height": 80,
                "resizeMode": "contain",
                "width": 80,
              }
            }
          />
        </View>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "opacity": 1,
            }
          }
        >
          <Image
            source={
              {
                "testUri": "../../../assets/X-.png",
              }
            }
            style={
              {
                "height": 80,
                "resizeMode": "contain",
                "width": 80,
              }
            }
          />
        </View>
      </View>
    </View>
  </View>
</View>
`;
