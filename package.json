{"name": "alrayyanfm", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-community/netinfo": "^11.2.1", "@testing-library/react-native": "^12.4.2", "install": "^0.13.0", "react": "18.2.0", "react-native": "0.73.0", "react-native-gesture-handler": "^2.14.1", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "^4.8.0", "react-native-track-player": "^4.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "^0.73.18", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@testing-library/jest-native": "^5.4.3", "@types/jest": "^29.5.11", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.55.0", "jest": "^29.7.0", "prettier": "^2.8.8", "react-test-renderer": "^18.2.0", "ts-jest": "^29.1.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "description": "", "main": "index.js", "author": "", "license": "ISC"}