module.exports = {
    testEnvironment: "node",
    preset: "react-native",
 
    verbose: true,
    transformIgnorePatterns: [
        "/node_modules/(?!@react|react-native|@react-native-community|@react-navigation|@sentry|@notifee|react-native-quick-crypto)"
    ],
    globals: {
        navigator: {
            userAgent: "node.js",
        },
    },
    moduleFileExtensions: ["js", "tsx", "ts"],
    moduleNameMapper: {
        "/^react-native-track-player$/": "<rootDir>/__mocks__/react-native-track-player.js"
    },
    automock: false,
    setupFilesAfterEnv: [
        "@testing-library/jest-native/extend-expect",
        "react-native-gesture-handler/jestSetup",
        "<rootDir>/jest/setup.js",
    ],

    
};
