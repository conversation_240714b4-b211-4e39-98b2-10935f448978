PODS:
  - boost (1.83.0)
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.73.0)
  - FBReactNativeSpec (0.73.0):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.0)
    - RCTTypeSafety (= 0.73.0)
    - React-Core (= 0.73.0)
    - React-jsi (= 0.73.0)
    - ReactCommon/turbomodule/core (= 0.73.0)
  - Flipper (0.201.0):
    - Flipper-Folly (~> 2.6)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (*******)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - FlipperKit (0.201.0):
    - FlipperKit/Core (= 0.201.0)
  - FlipperKit/Core (0.201.0):
    - Flipper (~> 0.201.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.201.0):
    - Flipper (~> 0.201.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.201.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.201.0)
  - FlipperKit/FKPortForwarding (0.201.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.201.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
  - FlipperKit/FlipperKitLayoutPlugin (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutTextSearchable (0.201.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.73.0):
    - hermes-engine/Pre-built (= 0.73.0)
  - hermes-engine/Pre-built (0.73.0)
  - libevent (2.1.12)
  - OpenSSL-Universal (1.1.1100)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.73.0)
  - RCTTypeSafety (0.73.0):
    - FBLazyVector (= 0.73.0)
    - RCTRequired (= 0.73.0)
    - React-Core (= 0.73.0)
  - React (0.73.0):
    - React-Core (= 0.73.0)
    - React-Core/DevSupport (= 0.73.0)
    - React-Core/RCTWebSocket (= 0.73.0)
    - React-RCTActionSheet (= 0.73.0)
    - React-RCTAnimation (= 0.73.0)
    - React-RCTBlob (= 0.73.0)
    - React-RCTImage (= 0.73.0)
    - React-RCTLinking (= 0.73.0)
    - React-RCTNetwork (= 0.73.0)
    - React-RCTSettings (= 0.73.0)
    - React-RCTText (= 0.73.0)
    - React-RCTVibration (= 0.73.0)
  - React-callinvoker (0.73.0)
  - React-Codegen (0.73.0):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.0)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.0)
    - React-Core/RCTWebSocket (= 0.73.0)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.0)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.0)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.73.0):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.0)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.0)
    - React-jsi (= 0.73.0)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.0)
    - ReactCommon
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.73.0):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.0)
    - React-debug (= 0.73.0)
    - React-jsi (= 0.73.0)
    - React-jsinspector (= 0.73.0)
    - React-logger (= 0.73.0)
    - React-perflogger (= 0.73.0)
    - React-runtimeexecutor (= 0.73.0)
  - React-debug (0.73.0)
  - React-Fabric (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.0)
    - React-Fabric/attributedstring (= 0.73.0)
    - React-Fabric/componentregistry (= 0.73.0)
    - React-Fabric/componentregistrynative (= 0.73.0)
    - React-Fabric/components (= 0.73.0)
    - React-Fabric/core (= 0.73.0)
    - React-Fabric/imagemanager (= 0.73.0)
    - React-Fabric/leakchecker (= 0.73.0)
    - React-Fabric/mounting (= 0.73.0)
    - React-Fabric/scheduler (= 0.73.0)
    - React-Fabric/telemetry (= 0.73.0)
    - React-Fabric/templateprocessor (= 0.73.0)
    - React-Fabric/textlayoutmanager (= 0.73.0)
    - React-Fabric/uimanager (= 0.73.0)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.0)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.0)
    - React-Fabric/components/modal (= 0.73.0)
    - React-Fabric/components/rncore (= 0.73.0)
    - React-Fabric/components/root (= 0.73.0)
    - React-Fabric/components/safeareaview (= 0.73.0)
    - React-Fabric/components/scrollview (= 0.73.0)
    - React-Fabric/components/text (= 0.73.0)
    - React-Fabric/components/textinput (= 0.73.0)
    - React-Fabric/components/unimplementedview (= 0.73.0)
    - React-Fabric/components/view (= 0.73.0)
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.0)
    - RCTTypeSafety (= 0.73.0)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.73.0)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.0):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.0)
    - React-utils
  - React-hermes (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - RCT-Folly/Futures (= 2022.05.16.00)
    - React-cxxreact (= 0.73.0)
    - React-jsi
    - React-jsiexecutor (= 0.73.0)
    - React-jsinspector (= 0.73.0)
    - React-perflogger (= 0.73.0)
  - React-ImageManager (0.73.0):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.73.0):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.0):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.0)
    - React-jsi (= 0.73.0)
    - React-perflogger (= 0.73.0)
  - React-jsinspector (0.73.0)
  - React-logger (0.73.0):
    - glog
  - React-Mapbuffer (0.73.0):
    - glog
    - React-debug
  - React-nativeconfig (0.73.0)
  - React-NativeModulesApple (0.73.0):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.0)
  - React-RCTActionSheet (0.73.0):
    - React-Core/RCTActionSheetHeaders (= 0.73.0)
  - React-RCTAnimation (0.73.0):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.0):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.0):
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.0):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.0):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.0)
    - React-jsi (= 0.73.0)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.0)
  - React-RCTNetwork (0.73.0):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.0):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.0):
    - React-Core/RCTTextHeaders (= 0.73.0)
    - Yoga
  - React-RCTVibration (0.73.0):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.0)
  - React-runtimeexecutor (0.73.0):
    - React-jsi (= 0.73.0)
  - React-runtimescheduler (0.73.0):
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.0):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.0):
    - React-logger (= 0.73.0)
    - ReactCommon/turbomodule (= 0.73.0)
  - ReactCommon/turbomodule (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.0)
    - React-cxxreact (= 0.73.0)
    - React-jsi (= 0.73.0)
    - React-logger (= 0.73.0)
    - React-perflogger (= 0.73.0)
    - ReactCommon/turbomodule/bridging (= 0.73.0)
    - ReactCommon/turbomodule/core (= 0.73.0)
  - ReactCommon/turbomodule/bridging (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.0)
    - React-cxxreact (= 0.73.0)
    - React-jsi (= 0.73.0)
    - React-logger (= 0.73.0)
    - React-perflogger (= 0.73.0)
  - ReactCommon/turbomodule/core (0.73.0):
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - hermes-engine
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.0)
    - React-cxxreact (= 0.73.0)
    - React-jsi (= 0.73.0)
    - React-logger (= 0.73.0)
    - React-perflogger (= 0.73.0)
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (= 0.201.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= *******)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - FlipperKit (= 0.201.0)
  - FlipperKit/Core (= 0.201.0)
  - FlipperKit/CppBridge (= 0.201.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.201.0)
  - FlipperKit/FBDefines (= 0.201.0)
  - FlipperKit/FKPortForwarding (= 0.201.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.201.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.201.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.201.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.201.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.201.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.201.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.201.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - OpenSSL-Universal (= 1.1.1100)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - FlipperKit
    - fmt
    - libevent
    - OpenSSL-Universal
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-11-17-RNv0.73.0-21043a3fc062be445e56a2c10ecd8be028dd9cc5
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 26fad476bfa736552bbfa698a06cc530475c1505
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: fea03f2699887d960129cc54bba7e52542b6f953
  FBLazyVector: 39ba45baf4e398618f8b3a4bb6ba8fcdb7fc2133
  FBReactNativeSpec: 20cfca68498e27879514790359289d1df2b52c56
  Flipper: c7a0093234c4bdd456e363f2f19b2e4b27652d44
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 2dc99b02f658daf147069aad9dbd29d8feb06d30
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 70c50ce58ddaf67dc35180db05f191692570f446
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  FlipperKit: 37525a5d056ef9b93d1578e04bc3ea1de940094f
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: c5d68082e772fa1c511173d6b30a9de2c05a69a2
  hermes-engine: 34304f8c6e8fa68f63a5fe29af82f227d817d7a7
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  RCT-Folly: 7169b2b1c44399c76a47b5deaaba715eeeb476c0
  RCTRequired: 5e3631b27c08716986980ef23eed8abdee1cdcaf
  RCTTypeSafety: 02a64828b0b428eb4f63de1397d44fb2d0747e85
  React: df5dbfbd10c5bd8d4bcb49bd9830551533e11c7e
  React-callinvoker: dc0dff59e8d3d1fe4cd9fb5f120f82a775d2a325
  React-Codegen: 88bf5d1e29db28c1c9b88fe909f073be6c9f769d
  React-Core: 276ccbbf282538138f4429313bb1200a15067c6e
  React-CoreModules: 64747180c0329bebed8307ffdc97c331220277a6
  React-cxxreact: 84d98283f701bae882dcd3ad7c573a02f4c9d5c0
  React-debug: 443cf46ade52f3555dd1ec709718793490ac5edc
  React-Fabric: 4c877c032b3acc07ed3f2e46ae25b5a39af89382
  React-FabricImage: c46c47ea3c672b9fadd6850795a51d3d9e5df712
  React-graphics: e1cff03acf09098513642535324432d495b6425c
  React-hermes: e3356f82c76c5c41688a7e08ced2254a944501c4
  React-ImageManager: c783771479ab0bf1e3dbe711cc8b9f5b0f65972b
  React-jserrorhandler: 7cd93ce5165e5d66c87b6f612f94e5642f5c5028
  React-jsi: 81b5fe94500e69051c2f3a775308afaa53e2608b
  React-jsiexecutor: 4f790f865ad23fa949396c1a103d06867c0047ed
  React-jsinspector: 9f6fb9ed9f03a0fb961ab8dc2e0e0ee0dc729e77
  React-logger: 008caec0d6a587abc1e71be21bfac5ba1662fe6a
  React-Mapbuffer: 58fe558faf52ecde6705376700f848d0293d1cef
  React-nativeconfig: a063483672b8add47a4875b0281e202908ff6747
  React-NativeModulesApple: 169506a5fd708ab22811f76ee06a976595c367a1
  React-perflogger: b61e5db8e5167f5e70366e820766c492847c082e
  React-RCTActionSheet: dcaecff7ffc1888972cd1c1935751ff3bce1e0c1
  React-RCTAnimation: 24b8ae7ebc897ba3f33a93a020bbc66ab7863f5d
  React-RCTAppDelegate: 661fc59d833e6727cc8c7e36bf8664215e5c277f
  React-RCTBlob: 112880abc731c5a0d8eefb5919a591ad30f630e8
  React-RCTFabric: a0289e3bf73da8c03b68b4e9733ba497b021de45
  React-RCTImage: b8065c1b51cc6c2ff58ad81001619352518dd793
  React-RCTLinking: fdf9f43f8bd763d178281a079700105674953849
  React-RCTNetwork: ad3d988e425288492510ee37c9dcdf8259566214
  React-RCTSettings: 67c3876f2775d1cf86298f657e6006afc2a2e4cf
  React-RCTText: 671518da40bd548943ec12ee6a60f733a751e2e9
  React-RCTVibration: 60bc4d01d7d8ab7cff14852a195a7fa93b38e1f3
  React-rendererdebug: 6aaab394c9fefe395ef61809580a9bf63b98fd3e
  React-rncore: 6680f0ebb941e256af7dc92c6a512356e77bfea7
  React-runtimeexecutor: 2ca6f02d3fd6eea5b9575eb30720cf12c5d89906
  React-runtimescheduler: 77543c74df984ce56c09d49d427149c53784aaf6
  React-utils: 42708ea436853045ef1eaff29996813d9fbbe209
  ReactCommon: 851280fb976399ca1aabc74cc2c3612069ea70a2
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: 20d6a900dcc8d61d5e3b799bbf627cc34474a8c4

PODFILE CHECKSUM: b118e49ccf42f036cba27fb2163372f1d41ce13a

COCOAPODS: 1.14.3
