# Al Rayyan.FM hybrid mobile app

Al Rayyan.FM hybrid mobile app.
Application is built using React Native.

## Screens

- Splash Screen
- Home Screen [Play button screen]

## Setup instructions

### 1. Install dependencies

```
mkdir al-rayyan-app
cd al-rayyan-app
git clone https://git.citrusinformatics.com/RGB/al-rayyan/al-rayyan-fm-hybrid-mobile-app.git
cd al-rayyan-app 
npm install
```

### 2. Run the app 

Setup for iOS
```
cd ios
pod install
```
Run in iOS device
```
npx react-native run-ios "<Device name>"
```

Run in Android
```
npx react-native run-android
```
