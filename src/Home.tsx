import React, { useEffect, useState } from "react";
import {
    View,
    Image,
    TouchableOpacity,
    Linking,
    StyleSheet,
    Alert,
    Platform,
} from "react-native";
import { Dimensions } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import TrackPlayer, { AppKilledPlaybackBehavior, Capability, Event, State, TrackType, useTrackPlayerEvents } from "react-native-track-player";
import { hp, wp } from "./utils/utils";
import NetInfo from "@react-native-community/netinfo";
import { Constants } from "./utils/Constants";

const screenWidth = Dimensions.get("window").width;
const events = [
    Event?.PlaybackState,
    Event?.PlaybackError,
    Event?.RemoteDuck,
];

const Home = () => {
    const [isPlaying, setIsPlaying] = useState(false);
    const { top } = useSafeAreaInsets();
    const [isConnected, setIsConnected] = useState(true);

    useEffect(() => {
        setupPlayer();
        const unsubscribe = NetInfo?.addEventListener((state) => {
            if (state.isConnected !== null) {
                setIsConnected(state.isConnected);
            }
            console.log(unsubscribe);
        });
        return () => {
            TrackPlayer.pause();
        };
    }, []);

    useTrackPlayerEvents(events, (event) => {
        if (Platform.OS === "ios" && "paused" in event && event.type === Event.RemoteDuck) {
            setIsPlaying(false);
        }
        if (event.type === Event.PlaybackError) {
            // setIsPlaying(false);
            TrackPlayer?.play();
        }
        if ("state" in event) {
            if(event.state === State.Paused){
                setIsPlaying(false);
            } else if(event.state === State.Playing){
                setIsPlaying(true);
                TrackPlayer.updateOptions({
                    android: {
                        appKilledPlaybackBehavior: AppKilledPlaybackBehavior.StopPlaybackAndRemoveNotification,
                    },
                    capabilities: [Capability.Play, Capability.Pause],
                    compactCapabilities: [Capability.Play, Capability.Pause],
                    notificationCapabilities: [Capability.Play, Capability.Pause],
                    progressUpdateEventInterval: 2,
                });
            }
        }
    });
  
    const setupPlayer = async () => {
        try {
            await TrackPlayer.setupPlayer({ autoHandleInterruptions: true });
            await TrackPlayer.add({
                id: Constants.id,
                url: Constants.appUrl,
                title: Constants.title,
                artist: Constants.artist,
                type: TrackType.HLS,
                artwork: require("../assets/appIcon.png"),
            });
        } catch (error) {
            console.error("Error setting up player:", error);
        }
    };

    const playAudio = async () => {
        if (isPlaying) {
            await TrackPlayer.pause();
            setIsPlaying(false);
        } else {
            if (!isConnected) {
                Alert.alert(
                    Constants?.networkAlert.title,
                    Constants?.networkAlert.message,
                    [
                        {
                            text: "OK",
                            onPress: () => {console.log("Tap ok");
                            },
                        },
                    ],
                );
            } else {
                try {
                    await TrackPlayer.play();
                    setIsPlaying(true);
                } catch (error) {
                    setIsPlaying(false);
                }
            }
        }
    };

    return (
        <View style={styles.container}>
            <Image
                source={require("../assets/pa.png")}
                style={[
                    styles.topText,
                    {
                        marginTop: top,
                    },
                ]}
            />

            <View style={styles.view1}>
                <View style={styles.view2}></View>
                <View style={styles.view3}></View>
            </View>

            <View style={styles.logoView}>
                <Image source={require("../assets/Br.png")} resizeMode='contain' style={styles.logo} />
            </View>

            <Image source={require("../assets/tT.png")} style={styles.bgImage} />

            <View style={{ flex: 1 }}>
                <View style={styles.roundView}>
                    <TouchableOpacity
                        testID="play-button"
                        onPress={() => {
                            playAudio();
                        }}
                    >
                        <Image
                            source={
                                !isPlaying
                                    ? require("../assets/Vd.png")
                                    : require("../assets/EQ.png")
                            }
                        />
                    </TouchableOpacity>
                </View>
                <View style={{ flex: 1, alignItems: "center" }}>
                    <View
                        style={styles.roundedView}
                    >

                        <TouchableOpacity
                            style={styles.bottomIcons}
                            testID="Facebook"
                            onPress={() => {
                                Linking.openURL(Constants?.externalLinks.fbUrl);
                            }}
                        >
                            <Image
                                source={require("../assets/aa.png")}
                                style={styles.bottomIcons}
                            />
                        </TouchableOpacity>
                        <TouchableOpacity
                            testID="Instagram"
                            onPress={() => {
                                Linking.openURL(Constants?.externalLinks.instalUrl);
                            }}
                        >
                            <Image
                                source={require("../assets/g6.png")}
                                style={styles.bottomIcons}
                            />
                        </TouchableOpacity>
                        <TouchableOpacity
                            testID="Twitter"
                            onPress={() => {
                                Linking.openURL(Constants?.externalLinks.twitterUrl);
                            }}
                        >
                            <Image
                                source={require("../assets/_e1.png")}
                                style={styles.bottomIcons}
                            />
                        </TouchableOpacity>
                        <TouchableOpacity
                            testID="YouTube"
                            onPress={() => {
                                Linking.openURL(Constants?.externalLinks.youtubeUrl);
                            }}
                        >
                            <Image
                                source={require("../assets/Gz.png")}
                                style={styles.bottomIcons}
                            />
                        </TouchableOpacity>
  
                        <TouchableOpacity
                            testID="Snapchat"
                            onPress={() => {
                                Linking.openURL(Constants?.externalLinks.snapChatUrl);
                            }}
                        >
                            <Image
                                source={require("../assets/X-.png")}
                                style={styles.bottomIcons}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </View>
    );
};
const styles = StyleSheet.create({
    container: {
        backgroundColor: "white",
        flex: 1,
    },
    topText: {
        alignSelf: "flex-end",
        marginRight: 20,
        height: hp(15),
        width: wp(80),
        marginBottom:hp(2)
    },
    view1: {
        height: 5,
        width: screenWidth,
        flexDirection: "row",
    },
    view2: {
        backgroundColor: "#DFDFDF",
        height: 5,
        width: screenWidth - wp(68),
    },
    view3: {
        backgroundColor: "#d2003e",
        height: 5,
        width: screenWidth - (screenWidth - wp(72)),
    },
    bgImage: {
        bottom: 0,
        height: hp(40),
        width: screenWidth,
        position: "absolute",
    },
    logoView: {
        height: hp(25),
        width: screenWidth,
        alignItems: "center",
    },
    logo: {
        height: hp(15),
        width: wp(45),
        resizeMode:"contain",
        marginTop:20
    },
    roundView: {
        width: wp(30),
        height: wp(30),
        borderRadius: wp(30),
        backgroundColor: "#d2003e",
        alignItems: "center",
        justifyContent: "center",
        alignSelf:"center",
        marginTop:30
    },
    roundedView: {
        width: screenWidth - 80,
        height: hp(8),
        borderBottomLeftRadius: 10,
        borderTopLeftRadius: 10,
        borderBottomRightRadius: 10,
        backgroundColor: "#d2003e",
        flexDirection: "row",
        justifyContent: "space-around",
        alignItems:"center",
        position:"absolute",
        bottom:20
    },
    bottomIcons: {
        width: hp(6),
        height: hp(6),
        resizeMode:"contain"
    },
});

export default Home;
